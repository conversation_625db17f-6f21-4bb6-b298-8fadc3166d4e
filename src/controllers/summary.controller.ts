import { ZodError } from "zod/v4";
import type { AuthenticatedRequest } from "../middleware/auth.middleware";
import {
  SummaryRequestSchema,
  type SummaryRequestInput,
} from "../schema/summary.schema";
import {
  GenerateSummaryService,
  GetSummaryByIdService,
  GetUserSummariesService,
  DeleteSummaryService,
} from "../services/summary.service";
import { AppError } from "../utils/error";

/**
 * Generate a summary for content
 * POST /api/v1/summaries
 */
export async function GenerateSummaryController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const userId = req.user!.id;

    const validSummaryData: SummaryRequestInput =
      SummaryRequestSchema.parse(body);

    const summary = await GenerateSummaryService(userId, validSummaryData);

    return Response.json(
      {
        success: true,
        message: "Summary generated successfully.",
        data: summary,
      },
      {
        status: summary.status === "completed" ? 200 : 202, // 202 for pending
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    console.error("Generate summary error:", error);

    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed.",
          errors: error.issues.map((err) => ({
            field: err.path.join("."),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    return Response.json(
      {
        success: false,
        message: "Failed to generate summary.",
      },
      { status: 500 }
    );
  }
}

/**
 * Get summary by ID
 * GET /api/v1/summaries/:id
 */
export async function GetSummaryByIdController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const summaryId = url.pathname.split("/").pop();
    const userId = req.user!.id;

    if (!summaryId) {
      return Response.json(
        {
          success: false,
          message: "Summary ID is required.",
        },
        { status: 400 }
      );
    }

    const summary = await GetSummaryByIdService(userId, summaryId);

    return Response.json(
      {
        success: true,
        message: "Summary retrieved successfully.",
        data: summary,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    console.error("Get summary error:", error);

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    return Response.json(
      {
        success: false,
        message: "Failed to retrieve summary.",
      },
      { status: 500 }
    );
  }
}

/**
 * Get all summaries for authenticated user
 * GET /api/v1/summaries
 */
export async function GetUserSummariesController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1", 10);
    const limit = parseInt(url.searchParams.get("limit") || "10", 10);
    const userId = req.user!.id;

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return Response.json(
        {
          success: false,
          message:
            "Invalid pagination parameters. Page must be >= 1, limit must be 1-100.",
        },
        { status: 400 }
      );
    }

    const result = await GetUserSummariesService(userId, page, limit);

    return Response.json(
      {
        success: true,
        message: "Summaries retrieved successfully.",
        data: result,
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    console.error("Get user summaries error:", error);

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    return Response.json(
      {
        success: false,
        message: "Failed to retrieve summaries.",
      },
      { status: 500 }
    );
  }
}

/**
 * Delete summary by ID
 * DELETE /api/v1/summaries/:id
 */
export async function DeleteSummaryController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const url = new URL(req.url);
    const summaryId = url.pathname.split("/").pop();
    const userId = req.user!.id;

    if (!summaryId) {
      return Response.json(
        {
          success: false,
          message: "Summary ID is required.",
        },
        { status: 400 }
      );
    }

    await DeleteSummaryService(userId, summaryId);

    return Response.json(
      {
        success: true,
        message: "Summary deleted successfully.",
      },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    console.error("Delete summary error:", error);

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    return Response.json(
      {
        success: false,
        message: "Failed to delete summary.",
      },
      { status: 500 }
    );
  }
}

/**
 * Generate quick summary for content
 * POST /api/v1/summaries/quick
 */
export async function GenerateQuickSummaryController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const userId = req.user!.id;

    // Force type to be "quick"
    const summaryData = { ...(body as object), type: "quick" };
    const validSummaryData: SummaryRequestInput =
      SummaryRequestSchema.parse(summaryData);

    const summary = await GenerateSummaryService(userId, validSummaryData);

    return Response.json(
      {
        success: true,
        message: "Quick summary generated successfully.",
        data: summary,
      },
      {
        status: summary.status === "completed" ? 200 : 202,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    console.error("Generate quick summary error:", error);

    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed.",
          errors: error.issues.map((err) => ({
            field: err.path.join("."),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    return Response.json(
      {
        success: false,
        message: "Failed to generate quick summary.",
      },
      { status: 500 }
    );
  }
}

/**
 * Generate detailed summary for content
 * POST /api/v1/summaries/detailed
 */
export async function GenerateDetailedSummaryController(
  req: AuthenticatedRequest
): Promise<Response> {
  try {
    const body = await req.json();
    const userId = req.user!.id;

    // Force type to be "detailed"
    const summaryData = { ...(body as object), type: "detailed" };
    const validSummaryData: SummaryRequestInput =
      SummaryRequestSchema.parse(summaryData);

    const summary = await GenerateSummaryService(userId, validSummaryData);

    return Response.json(
      {
        success: true,
        message: "Detailed summary generated successfully.",
        data: summary,
      },
      {
        status: summary.status === "completed" ? 200 : 202,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Server-Version": "1.0.0",
        },
      }
    );
  } catch (error) {
    console.error("Generate detailed summary error:", error);

    if (error instanceof ZodError) {
      return Response.json(
        {
          success: false,
          message: "Validation failed.",
          errors: error.issues.map((err) => ({
            field: err.path.join("."),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    if (error instanceof AppError) {
      return Response.json(
        {
          success: false,
          message: error.message,
        },
        { status: error.statusCode }
      );
    }

    return Response.json(
      {
        success: false,
        message: "Failed to generate detailed summary.",
      },
      { status: 500 }
    );
  }
}
