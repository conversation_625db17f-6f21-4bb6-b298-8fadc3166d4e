import { Types } from "mongoose";
import { Content } from "../models/content.model";
import { User } from "../models/user.model";
import { Tags } from "../models/tags.model";
import type {
  ContentTypes,
  ContentUpdateTypes,
} from "../schema/content.schema";
import {
  BadRequestError,
  NotFoundError,
  UnathorizedError,
} from "../utils/error";

/**
 * Clean up orphaned tag references from content
 */
export async function cleanupOrphanedTagReferences(userId: string) {
  try {
    // Get all content for the user
    const userContent = await Content.find({ userId });

    for (const content of userContent) {
      if (content.tags && content.tags.length > 0) {
        // Check which tags still exist
        const existingTags = await Tags.find({
          _id: { $in: content.tags },
          userId: userId,
        });

        const existingTagIds = existingTags.map((tag) =>
          (tag._id as Types.ObjectId).toString()
        );
        const validTags = content.tags.filter((tagId) =>
          existingTagIds.includes(tagId.toString())
        );

        // Update content if some tags were removed
        if (validTags.length !== content.tags.length) {
          await Content.findByIdAndUpdate(content._id, {
            tags: validTags,
          });
        }
      }
    }
  } catch (error) {
    console.warn("Failed to cleanup orphaned tag references:", error);
  }
}

/**
 * Create single User content with user authorization
 */
export async function CreateContentService(
  id: string | undefined,
  body: ContentTypes
) {
  const existingUser = await User.findById(id);

  if (!existingUser) {
    throw new BadRequestError("User not found.");
  }

  // Validate that all tags belong to the user
  if (body.tags && body.tags.length > 0) {
    const userTags = await Tags.find({
      _id: { $in: body.tags },
      userId: id,
    });

    if (userTags.length !== body.tags.length) {
      const foundTagIds = userTags.map((tag) =>
        (tag._id as Types.ObjectId).toString()
      );
      const invalidTags = body.tags.filter(
        (tagId) => !foundTagIds.includes(tagId.toString())
      );
      throw new BadRequestError(
        `Some tags don't belong to you or don't exist: ${invalidTags.join(
          ", "
        )}`
      );
    }
  }

  const contentData = new Content(body);
  await contentData.save();

  const { userId: _, ...contentDataFiltered } = contentData.toObject();

  return contentDataFiltered;
}

/**
 * Get all user content by ID with user authorization and pagination
 */
export async function GetUserContentService(
  id: string | undefined,
  page: number = 1,
  limit: number = 20,
  search?: string,
  type?: string,
  sortBy: string = "createdAt",
  sortOrder: "asc" | "desc" = "desc"
) {
  const existingUser = await User.findById(id);

  if (!existingUser) {
    throw new BadRequestError("User not found");
  }

  const skip = (page - 1) * limit;

  // Build query
  const query: any = { userId: id };

  if (search) {
    query.$or = [
      { title: { $regex: search, $options: "i" } },
      { link: { $regex: search, $options: "i" } },
    ];
  }

  if (type) {
    query.type = type;
  }

  // Build sort object
  const sort: any = {};
  sort[sortBy] = sortOrder === "asc" ? 1 : -1;

  // Clean up any orphaned tag references first
  if (id) {
    await cleanupOrphanedTagReferences(id);
  }

  // Get content with pagination
  const [userContentData, totalCount] = await Promise.all([
    Content.find(query)
      .populate({
        path: "tags",
        select: "title",
        match: { userId: id }, // Only populate tags that belong to the user
      })
      .select("-userId -__v")
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean()
      .exec(),
    Content.countDocuments(query),
  ]);

  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    content: userContentData,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      hasNextPage,
      hasPrevPage,
      limit,
    },
  };
}

/**
 * Update single content by ID with user authorization
 */
export async function UpdateUserContentService(
  contentId: string,
  userId: string,
  updateData: ContentUpdateTypes
) {
  if (!Types.ObjectId.isValid(contentId)) {
    throw new BadRequestError("Invalid Content Id format.");
  }
  if (!Types.ObjectId.isValid(userId)) {
    throw new BadRequestError("Invalid User Id format.");
  }

  const dataToUpdate: any = { ...updateData };
  if (dataToUpdate.tags) {
    // Validate tag IDs format
    dataToUpdate.tags = dataToUpdate.tags.map((tagId: string) => {
      if (!Types.ObjectId.isValid(tagId)) {
        throw new BadRequestError(`Invalid Tag Id format: ${tagId}`);
      }
      return new Types.ObjectId(tagId);
    });

    // Validate that all tags belong to the user
    const userTags = await Tags.find({
      _id: { $in: dataToUpdate.tags },
      userId: new Types.ObjectId(userId),
    });

    if (userTags.length !== dataToUpdate.tags.length) {
      const foundTagIds = userTags.map((tag) =>
        (tag._id as Types.ObjectId).toString()
      );
      const invalidTags = dataToUpdate.tags.filter(
        (tagId: Types.ObjectId) => !foundTagIds.includes(tagId.toString())
      );
      throw new BadRequestError(
        `Some tags don't belong to you or don't exist: ${invalidTags.join(
          ", "
        )}`
      );
    }
  }

  const updatedContent = await Content.findOneAndUpdate(
    { _id: new Types.ObjectId(contentId), userId: new Types.ObjectId(userId) },
    { $set: dataToUpdate },
    { new: true, runValidators: true }
  );

  if (!updatedContent) {
    const contentExists = await Content.findById(contentId);
    if (!contentExists) {
      throw new NotFoundError("Content not found.");
    } else {
      throw new UnathorizedError(
        "You are not authorized to update this content."
      );
    }
  }

  return updatedContent.toObject();
}

/**
 * Get single Content by Id
 * not yet found a use case
 */
export async function GetContentService(contentId: string) {
  const contentData = await Content.findById(contentId).lean();

  if (!contentData) {
    throw new NotFoundError("Content not found.");
  }

  return contentData;
}

/**
 * Get single content by ID with user authorization
 */
export async function GetContentByIdService(contentId: string, userId: string) {
  try {
    if (!Types.ObjectId.isValid(contentId)) {
      throw new BadRequestError("Invalid content ID format.");
    }

    const content = await Content.findOne({
      _id: contentId,
      userId: userId,
    })
      .populate({
        path: "tags",
        select: "title",
        match: { userId: userId }, // Only populate tags that belong to the user
      })
      .select("-userId -__v")
      .lean();

    if (!content) {
      const contentExists = await Content.findById(contentId);
      if (!contentExists) {
        throw new NotFoundError("Content not found.");
      } else {
        throw new UnathorizedError(
          "You are not authorized to update this content."
        );
      }
    }

    return content;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to fetch content.");
  }
}

/**
 * Delete content by ID
 */
export async function DeleteContentService(contentId: string, userId: string) {
  try {
    if (!Types.ObjectId.isValid(contentId)) {
      throw new BadRequestError("Invalid content ID format.");
    }

    const deletedContent = await Content.findOneAndDelete({
      _id: contentId,
      userId: userId,
    }).lean();

    if (!deletedContent) {
      const contentExists = await Content.findById(contentId);
      if (!contentExists) {
        throw new NotFoundError("Content not found.");
      } else {
        throw new UnathorizedError(
          "You are not authorized to update this content."
        );
      }
    }

    // Also delete any associated shareable links
    try {
      const { Link } = await import("../models/link.model");
      await Link.deleteMany({ contentId: contentId });
    } catch (error) {
      console.warn("Failed to delete associated shareable links:", error);
    }

    return deletedContent;
  } catch (error) {
    if (error instanceof BadRequestError || error instanceof NotFoundError) {
      throw error;
    }
    throw new BadRequestError("Failed to delete content.");
  }
}

/**
 * Bulk delete content
 */
export async function BulkDeleteContentService(
  contentIds: string[],
  userId: string
) {
  try {
    // Validate all content IDs
    const invalidIds = contentIds.filter((id) => !Types.ObjectId.isValid(id));
    if (invalidIds.length > 0) {
      throw new BadRequestError(
        `Invalid content ID format: ${invalidIds.join(", ")}`
      );
    }

    const result = await Content.deleteMany({
      _id: { $in: contentIds.map((id) => new Types.ObjectId(id)) },
      userId: userId,
    });

    // Also delete any associated shareable links
    try {
      const { Link } = await import("../models/link.model");
      await Link.deleteMany({ contentId: { $in: contentIds } });
    } catch (error) {
      console.warn("Failed to delete associated shareable links:", error);
    }

    return {
      deletedCount: result.deletedCount,
      requestedCount: contentIds.length,
    };
  } catch (error) {
    if (error instanceof BadRequestError) {
      throw error;
    }
    throw new BadRequestError("Failed to bulk delete content.");
  }
}

/**
 * Get content statistics for user
 */
export async function GetContentStatsService(userId: string) {
  try {
    const [totalContent, contentByType, recentContent] = await Promise.all([
      Content.countDocuments({ userId }),
      Content.aggregate([
        { $match: { userId: new Types.ObjectId(userId) } },
        { $group: { _id: "$type", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
      ]),
      Content.find({ userId })
        .sort({ createdAt: -1 })
        .limit(5)
        .select("title type createdAt")
        .lean(),
    ]);

    return {
      totalContent,
      contentByType,
      recentContent,
    };
  } catch (error) {
    throw new BadRequestError("Failed to get content statistics.");
  }
}
