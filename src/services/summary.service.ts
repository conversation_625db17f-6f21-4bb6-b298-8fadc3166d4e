import { Types } from "mongoose";
import { Summary } from "../models/summary.model";
import { Content } from "../models/content.model";
import { AIService } from "./ai.service";
import {
  NotFoundError,
  BadRequestError,
  ConflictError,
  ServiceUnavailableError,
} from "../utils/error";
import type { SummaryRequestInput } from "../schema/summary.schema";

export interface SummaryResult {
  id: string;
  contentId: string;
  userId: string;
  type: "quick" | "detailed";
  summary: string;
  originalUrl: string;
  contentTitle: string;
  tokensUsed: number;
  processingTime: number;
  model: string;
  status: "pending" | "completed" | "failed";
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Generate a summary for content
 */
export async function GenerateSummaryService(
  userId: string,
  summaryData: SummaryRequestInput
): Promise<SummaryResult> {
  const startTime = Date.now();

  try {
    // Validate user ID format
    if (!Types.ObjectId.isValid(userId)) {
      throw new BadRequestError("Invalid user ID format.");
    }

    // Validate content ID format
    if (!Types.ObjectId.isValid(summaryData.contentId)) {
      throw new BadRequestError("Invalid content ID format.");
    }

    // Check if content exists and belongs to user
    const content = await Content.findOne({
      _id: summaryData.contentId,
      userId: userId,
    });

    if (!content) {
      throw new NotFoundError(
        "Content not found or you don't have permission to access it."
      );
    }

    // Check if summary already exists
    const existingSummary = await Summary.findOne({
      contentId: summaryData.contentId,
      userId: userId,
      type: summaryData.type,
    });

    if (existingSummary && !summaryData.forceRegenerate) {
      // Return existing summary
      return {
        id: existingSummary._id!.toString(),
        contentId: existingSummary.contentId.toString(),
        userId: existingSummary.userId.toString(),
        type: existingSummary.type as "quick" | "detailed",
        summary: existingSummary.summary,
        originalUrl: existingSummary.originalUrl,
        contentTitle: existingSummary.contentTitle,
        tokensUsed: existingSummary.tokensUsed,
        processingTime: existingSummary.processingTime,
        model: existingSummary.model,
        status: existingSummary.status as "pending" | "completed" | "failed",
        errorMessage: existingSummary.errorMessage,
        createdAt: existingSummary.createdAt!,
        updatedAt: existingSummary.updatedAt!,
      };
    }

    // Create or update summary record
    let summaryRecord;
    if (existingSummary && summaryData.forceRegenerate) {
      // Update existing record
      summaryRecord = await Summary.findByIdAndUpdate(
        existingSummary._id,
        {
          status: "pending",
          errorMessage: undefined,
          updatedAt: new Date(),
        },
        { new: true }
      );
    } else {
      // Create new summary record
      summaryRecord = new Summary({
        contentId: summaryData.contentId,
        userId: userId,
        type: summaryData.type,
        // Don't set summary initially - will be set after AI generation
        originalUrl: content.link,
        contentTitle: content.title,
        tokensUsed: 0,
        processingTime: 0,
        model: "gemini-1.5-flash-8b",
        status: "pending",
      });
      await summaryRecord.save();
    }

    try {
      // Generate summary using AI service
      console.log(`📝 Generating ${summaryData.type} summary for content: ${content.title}`);
      const aiResult = await AIService.generateSummary({
        type: summaryData.type,
        url: content.link,
        title: content.title,
      });

      console.log(`✅ AI service returned summary: ${aiResult.summary.substring(0, 100)}...`);
      console.log(`📊 Summary length: ${aiResult.summary.length} characters`);

      const processingTime = Date.now() - startTime;

      // Update summary record with results
      const updatedSummary = await Summary.findByIdAndUpdate(
        summaryRecord!._id,
        {
          summary: aiResult.summary,
          tokensUsed: aiResult.tokensUsed,
          processingTime: processingTime,
          model: aiResult.model,
          status: "completed",
          updatedAt: new Date(),
        },
        { new: true }
      );

      return {
        id: updatedSummary!._id!.toString(),
        contentId: updatedSummary!.contentId.toString(),
        userId: updatedSummary!.userId.toString(),
        type: updatedSummary!.type as "quick" | "detailed",
        summary: updatedSummary!.summary,
        originalUrl: updatedSummary!.originalUrl,
        contentTitle: updatedSummary!.contentTitle,
        tokensUsed: updatedSummary!.tokensUsed,
        processingTime: updatedSummary!.processingTime,
        model: updatedSummary!.model,
        status: updatedSummary!.status as "completed",
        createdAt: updatedSummary!.createdAt!,
        updatedAt: updatedSummary!.updatedAt!,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";

      // Update summary record with error
      await Summary.findByIdAndUpdate(summaryRecord!._id, {
        status: "failed",
        errorMessage: errorMessage,
        processingTime: processingTime,
        updatedAt: new Date(),
      });

      // Re-throw the error
      throw error;
    }
  } catch (error) {
    console.error("Summary generation error:", error);
    throw error;
  }
}

/**
 * Get summary by ID
 */
export async function GetSummaryByIdService(
  userId: string,
  summaryId: string
): Promise<SummaryResult> {
  if (!Types.ObjectId.isValid(userId) || !Types.ObjectId.isValid(summaryId)) {
    throw new BadRequestError("Invalid ID format.");
  }

  const summary = await Summary.findOne({
    _id: summaryId,
    userId: userId,
  });

  if (!summary) {
    throw new NotFoundError(
      "Summary not found or you don't have permission to access it."
    );
  }

  return {
    id: summary._id!.toString(),
    contentId: summary.contentId.toString(),
    userId: summary.userId.toString(),
    type: summary.type as "quick" | "detailed",
    summary: summary.summary,
    originalUrl: summary.originalUrl,
    contentTitle: summary.contentTitle,
    tokensUsed: summary.tokensUsed,
    processingTime: summary.processingTime,
    model: summary.model,
    status: summary.status as "pending" | "completed" | "failed",
    errorMessage: summary.errorMessage,
    createdAt: summary.createdAt!,
    updatedAt: summary.updatedAt!,
  };
}

/**
 * Get all summaries for a user
 */
export async function GetUserSummariesService(
  userId: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  summaries: SummaryResult[];
  total: number;
  page: number;
  totalPages: number;
}> {
  if (!Types.ObjectId.isValid(userId)) {
    throw new BadRequestError("Invalid user ID format.");
  }

  const skip = (page - 1) * limit;

  const [summaries, total] = await Promise.all([
    Summary.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Summary.countDocuments({ userId }),
  ]);

  const summaryResults: SummaryResult[] = summaries.map((summary) => ({
    id: summary._id!.toString(),
    contentId: summary.contentId.toString(),
    userId: summary.userId.toString(),
    type: summary.type as "quick" | "detailed",
    summary: summary.summary,
    originalUrl: summary.originalUrl,
    contentTitle: summary.contentTitle,
    tokensUsed: summary.tokensUsed,
    processingTime: summary.processingTime,
    model: summary.model,
    status: summary.status as "pending" | "completed" | "failed",
    errorMessage: summary.errorMessage,
    createdAt: summary.createdAt!,
    updatedAt: summary.updatedAt!,
  }));

  return {
    summaries: summaryResults,
    total,
    page,
    totalPages: Math.ceil(total / limit),
  };
}

/**
 * Delete summary by ID
 */
export async function DeleteSummaryService(
  userId: string,
  summaryId: string
): Promise<void> {
  if (!Types.ObjectId.isValid(userId) || !Types.ObjectId.isValid(summaryId)) {
    throw new BadRequestError("Invalid ID format.");
  }

  const result = await Summary.findOneAndDelete({
    _id: summaryId,
    userId: userId,
  });

  if (!result) {
    throw new NotFoundError(
      "Summary not found or you don't have permission to delete it."
    );
  }
}
