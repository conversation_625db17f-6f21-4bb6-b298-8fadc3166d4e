#!/usr/bin/env node

/**
 * Comprehensive API Route Testing Script
 * Tests all endpoints in the Second Brain application
 */

import { createRequire } from "module";
const require = createRequire(import.meta.url);

// Try to use built-in fetch (Node 18+) or fallback to http module
let fetch;
try {
  fetch = globalThis.fetch;
} catch {
  // Fallback for older Node versions
  const http = require("http");
  const https = require("https");
  const { URL } = require("url");

  fetch = async (url, options = {}) => {
    return new Promise((resolve, reject) => {
      const parsedUrl = new URL(url);
      const isHttps = parsedUrl.protocol === "https:";
      const client = isHttps ? https : http;

      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (isHttps ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || "GET",
        headers: options.headers || {},
      };

      const req = client.request(requestOptions, (res) => {
        let data = "";
        res.on("data", (chunk) => (data += chunk));
        res.on("end", () => {
          resolve({
            status: res.statusCode,
            headers: new Map(Object.entries(res.headers)),
            text: () => Promise.resolve(data),
            json: () => Promise.resolve(JSON.parse(data)),
          });
        });
      });

      req.on("error", reject);

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  };
}

const BASE_URL = "http://localhost:3000";

// Test data
const testUser = {
  fullName: "Test User",
  username: "testuser123",
  password: "TestPass123!",
};

const testContentData = {
  link: "https://example.com/test-article",
  type: "artical",
  title: "Test Article for API Testing",
};

const testTag = {
  title: "test-tag",
};

// Global variables to store tokens and IDs
let accessToken = "";
let refreshToken = "";
let userId = "";
let contentId = "";
let tagId = "";
let linkId = "";
let summaryId = "";

// Helper function to make HTTP requests
async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const responseData = await response.text();

    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }

    return {
      status: response.status,
      data: parsedData,
      headers: Object.fromEntries(response.headers.entries()),
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message,
      data: null,
    };
  }
}

// Helper function to log test results
function logTest(testName, response, expectedStatus = 200) {
  const status = response.status;
  const success = status === expectedStatus;
  const icon = success ? "✅" : "❌";

  console.log(`${icon} ${testName}`);
  console.log(`   Status: ${status} (expected: ${expectedStatus})`);

  if (response.error) {
    console.log(`   Error: ${response.error}`);
  } else if (response.data) {
    if (typeof response.data === "object") {
      console.log(
        `   Response: ${JSON.stringify(response.data, null, 2).substring(
          0,
          200
        )}...`
      );
    } else {
      console.log(`   Response: ${response.data}`);
    }
  }
  console.log("");

  return success;
}

// Test functions
async function testServerStatus() {
  console.log("🔍 Testing Server Status...\n");

  const rootResponse = await makeRequest("GET", "/");
  logTest("GET /", rootResponse);

  const statusResponse = await makeRequest("GET", "/api/status");
  logTest("GET /api/status", statusResponse);
}

async function testAuthentication() {
  console.log("🔐 Testing Authentication...\n");

  // Test signup
  const signupResponse = await makeRequest("POST", "/api/v1/signup", testUser);
  const signupSuccess = logTest("POST /api/v1/signup", signupResponse, 201);

  if (signupSuccess && signupResponse.data.accessToken) {
    accessToken = signupResponse.data.accessToken;
    userId = signupResponse.data.user.id;
  }

  // Test login
  const loginResponse = await makeRequest("POST", "/api/v1/login", {
    username: testUser.username,
    password: testUser.password,
  });
  const loginSuccess = logTest("POST /api/v1/login", loginResponse);

  if (loginSuccess && loginResponse.data.accessToken) {
    accessToken = loginResponse.data.accessToken;
  }

  // Test refresh token (if we have cookies)
  const refreshResponse = await makeRequest("POST", "/api/v1/refresh", {});
  logTest("POST /api/v1/refresh", refreshResponse);
}

async function testTags() {
  console.log("🏷️ Testing Tags Management...\n");

  const authHeaders = { Authorization: `Bearer ${accessToken}` };

  // Create tag
  const createResponse = await makeRequest(
    "POST",
    "/api/v1/tags",
    testTag,
    authHeaders
  );
  const createSuccess = logTest("POST /api/v1/tags", createResponse, 201);

  if (createSuccess && createResponse.data.tagData) {
    tagId = createResponse.data.tagData.id;
  }

  // Get all tags
  const getAllResponse = await makeRequest(
    "GET",
    "/api/v1/tags?page=1&limit=10",
    null,
    authHeaders
  );
  logTest("GET /api/v1/tags", getAllResponse);

  // Search tags
  const searchResponse = await makeRequest(
    "GET",
    "/api/v1/tags/search?q=test&limit=5",
    null,
    authHeaders
  );
  logTest("GET /api/v1/tags/search", searchResponse);

  if (tagId) {
    // Get tag by ID
    const getByIdResponse = await makeRequest(
      "GET",
      `/api/v1/tags/${tagId}`,
      null,
      authHeaders
    );
    logTest(`GET /api/v1/tags/${tagId}`, getByIdResponse);

    // Update tag
    const updateResponse = await makeRequest(
      "PUT",
      `/api/v1/tags/${tagId}`,
      { title: "updated-test-tag" },
      authHeaders
    );
    logTest(`PUT /api/v1/tags/${tagId}`, updateResponse);
  }
}

async function testContent() {
  console.log("📄 Testing Content Management...\n");

  const authHeaders = { Authorization: `Bearer ${accessToken}` };

  // Add tag to content if we have one
  const contentData = { ...testContentData };
  if (tagId) {
    contentData.tags = [tagId];
  }

  // Create content
  const createResponse = await makeRequest(
    "POST",
    "/api/v1/user/content",
    contentData,
    authHeaders
  );
  const createSuccess = logTest("POST /api/v1/user/content", createResponse);

  if (createSuccess && createResponse.data.contentData) {
    contentId = createResponse.data.contentData.id;
  }

  // Get user content
  const getAllResponse = await makeRequest(
    "GET",
    "/api/v1/user/content?page=1&limit=10",
    null,
    authHeaders
  );
  logTest("GET /api/v1/user/content", getAllResponse);

  // Get content stats
  const statsResponse = await makeRequest(
    "GET",
    "/api/v1/user/content/stats",
    null,
    authHeaders
  );
  logTest("GET /api/v1/user/content/stats", statsResponse);

  if (contentId) {
    // Get content by ID
    const getByIdResponse = await makeRequest(
      "GET",
      `/api/v1/user/content/${contentId}`,
      null,
      authHeaders
    );
    logTest(`GET /api/v1/user/content/${contentId}`, getByIdResponse);

    // Update content
    const updateResponse = await makeRequest(
      "PUT",
      `/api/v1/user/content/${contentId}`,
      { title: "Updated Test Article" },
      authHeaders
    );
    logTest(`PUT /api/v1/user/content/${contentId}`, updateResponse);
  }
}

async function testShareableLinks() {
  console.log("🔗 Testing Shareable Links...\n");

  const authHeaders = { Authorization: `Bearer ${accessToken}` };

  if (contentId) {
    // Create shareable link
    const createResponse = await makeRequest(
      "POST",
      "/api/v1/links",
      { contentId },
      authHeaders
    );
    const createSuccess = logTest("POST /api/v1/links", createResponse, 201);

    if (createSuccess && createResponse.data.data) {
      linkId = createResponse.data.data.id;
      const shortCode = createResponse.data.data.shortCode;

      // Test public access to shared content
      const accessResponse = await makeRequest("GET", `/share/${shortCode}`);
      logTest(`GET /share/${shortCode}`, accessResponse);
    }
  }
}

async function testAISummaries() {
  console.log("🤖 Testing AI Summaries...\n");

  const authHeaders = { Authorization: `Bearer ${accessToken}` };

  if (contentId) {
    // Generate quick summary
    const quickResponse = await makeRequest(
      "POST",
      "/api/v1/summaries/quick",
      { contentId },
      authHeaders
    );
    logTest("POST /api/v1/summaries/quick", quickResponse, [200, 202]);

    // Generate general summary
    const summaryResponse = await makeRequest(
      "POST",
      "/api/v1/summaries",
      { contentId, type: "quick" },
      authHeaders
    );
    const summarySuccess = logTest(
      "POST /api/v1/summaries",
      summaryResponse,
      [200, 202]
    );

    if (summarySuccess && summaryResponse.data.data) {
      summaryId = summaryResponse.data.data.id;
    }

    // Get user summaries
    const getAllResponse = await makeRequest(
      "GET",
      "/api/v1/summaries?page=1&limit=10",
      null,
      authHeaders
    );
    logTest("GET /api/v1/summaries", getAllResponse);

    if (summaryId) {
      // Get summary by ID
      const getByIdResponse = await makeRequest(
        "GET",
        `/api/v1/summaries/${summaryId}`,
        null,
        authHeaders
      );
      logTest(`GET /api/v1/summaries/${summaryId}`, getByIdResponse);
    }
  }
}

async function testCleanup() {
  console.log("🧹 Testing Cleanup (Delete Operations)...\n");

  const authHeaders = { Authorization: `Bearer ${accessToken}` };

  // Delete summary
  if (summaryId) {
    const deleteSummaryResponse = await makeRequest(
      "DELETE",
      `/api/v1/summaries/${summaryId}`,
      null,
      authHeaders
    );
    logTest(`DELETE /api/v1/summaries/${summaryId}`, deleteSummaryResponse);
  }

  // Delete shareable link
  if (linkId) {
    const deleteLinkResponse = await makeRequest(
      "DELETE",
      `/api/v1/links/${linkId}`,
      null,
      authHeaders
    );
    logTest(`DELETE /api/v1/links/${linkId}`, deleteLinkResponse);
  }

  // Delete content
  if (contentId) {
    const deleteContentResponse = await makeRequest(
      "DELETE",
      `/api/v1/user/content/${contentId}`,
      null,
      authHeaders
    );
    logTest(`DELETE /api/v1/user/content/${contentId}`, deleteContentResponse);
  }

  // Delete tag
  if (tagId) {
    const deleteTagResponse = await makeRequest(
      "DELETE",
      `/api/v1/tags/${tagId}`,
      null,
      authHeaders
    );
    logTest(`DELETE /api/v1/tags/${tagId}`, deleteTagResponse);
  }

  // Test bulk delete (with empty array since we deleted content)
  const bulkDeleteResponse = await makeRequest(
    "DELETE",
    "/api/v1/user/content/bulk",
    { contentIds: [] },
    authHeaders
  );
  logTest("DELETE /api/v1/user/content/bulk", bulkDeleteResponse);

  // Logout
  const logoutResponse = await makeRequest(
    "POST",
    "/api/v1/logout",
    {},
    authHeaders
  );
  logTest("POST /api/v1/logout", logoutResponse);
}

// Main test runner
async function runAllTests() {
  console.log("🚀 Starting Comprehensive API Route Testing...\n");
  console.log("=".repeat(60));

  try {
    await testServerStatus();
    await testAuthentication();
    await testTags();
    await testContent();
    await testShareableLinks();
    await testAISummaries();
    await testCleanup();

    console.log("=".repeat(60));
    console.log("✅ All tests completed!");
    console.log(
      "\nNote: Some AI summary tests might show 202 status (pending) which is normal."
    );
    console.log(
      "Rate limiting may affect some tests if run multiple times quickly."
    );
  } catch (error) {
    console.error("❌ Test execution failed:", error);
  }
}

// Check if server is running before starting tests
async function checkServer() {
  console.log("Checking if server is running...");
  const response = await makeRequest("GET", "/api/status");

  if (response.status === 0) {
    console.log(
      "❌ Server is not running! Please start the server with: bun run dev"
    );
    process.exit(1);
  }

  console.log("✅ Server is running!\n");
}

// Run the tests
checkServer().then(runAllTests);
