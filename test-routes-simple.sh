#!/bin/bash

# Comprehensive API Route Testing Script using curl
# Tests all endpoints in the Second Brain application

BASE_URL="http://localhost:3000"
ACCESS_TOKEN=""
USER_ID=""
CONTENT_ID=""
TAG_ID=""
LINK_ID=""
SUMMARY_ID=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_test() {
    local test_name="$1"
    local status_code="$2"
    local expected="$3"
    local response="$4"
    
    if [ "$status_code" = "$expected" ]; then
        echo -e "${GREEN}✅ $test_name${NC}"
        echo -e "   Status: $status_code (expected: $expected)"
    else
        echo -e "${RED}❌ $test_name${NC}"
        echo -e "   Status: $status_code (expected: $expected)"
    fi
    
    if [ ! -z "$response" ]; then
        echo -e "   Response: ${response:0:200}..."
    fi
    echo ""
}

# Function to make HTTP requests
make_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local auth_header="$4"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    if [ ! -z "$auth_header" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $auth_header'"
    fi
    
    curl_cmd="$curl_cmd $BASE_URL$endpoint"
    
    # Execute curl and capture response
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    echo "$status_code|$body"
}

# Check if server is running
echo "Checking if server is running..."
server_check=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/api/status")
if [ "$server_check" != "200" ]; then
    echo -e "${RED}❌ Server is not running! Please start the server with: bun run dev${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Server is running!${NC}"
echo ""

echo -e "${BLUE}🚀 Starting Comprehensive API Route Testing...${NC}"
echo "============================================================"

# Test Server Status
echo -e "${YELLOW}🔍 Testing Server Status...${NC}"
echo ""

result=$(make_request "GET" "/" "" "")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "GET /" "$status_code" "200" "$response"

result=$(make_request "GET" "/api/status" "" "")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "GET /api/status" "$status_code" "200" "$response"

# Test Authentication
echo -e "${YELLOW}🔐 Testing Authentication...${NC}"
echo ""

# Signup with unique username
TIMESTAMP=$(date +%s)
signup_data='{"fullName":"Test User","username":"testuser'$TIMESTAMP'","password":"TestPass123!"}'
result=$(make_request "POST" "/api/v1/signup" "$signup_data" "")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "POST /api/v1/signup" "$status_code" "201" "$response"

# Extract access token from signup response
if [ "$status_code" = "201" ]; then
    ACCESS_TOKEN=$(echo "$response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
    USER_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
fi

# Login
login_data='{"username":"testuser'$TIMESTAMP'","password":"TestPass123!"}'
result=$(make_request "POST" "/api/v1/login" "$login_data" "")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "POST /api/v1/login" "$status_code" "200" "$response"

# Update access token from login if successful
if [ "$status_code" = "200" ]; then
    ACCESS_TOKEN=$(echo "$response" | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)
fi

# Test Tags Management
echo -e "${YELLOW}🏷️ Testing Tags Management...${NC}"
echo ""

# Create tag
tag_data='{"title":"test-tag"}'
result=$(make_request "POST" "/api/v1/tags" "$tag_data" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "POST /api/v1/tags" "$status_code" "201" "$response"

# Extract tag ID
if [ "$status_code" = "201" ]; then
    TAG_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
fi

# Get all tags
result=$(make_request "GET" "/api/v1/tags?page=1&limit=10" "" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "GET /api/v1/tags" "$status_code" "200" "$response"

# Search tags
result=$(make_request "GET" "/api/v1/tags/search?q=test&limit=5" "" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "GET /api/v1/tags/search" "$status_code" "200" "$response"

# Get tag by ID
if [ ! -z "$TAG_ID" ]; then
    result=$(make_request "GET" "/api/v1/tags/$TAG_ID" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "GET /api/v1/tags/$TAG_ID" "$status_code" "200" "$response"
    
    # Update tag
    update_tag_data='{"title":"updated-test-tag"}'
    result=$(make_request "PUT" "/api/v1/tags/$TAG_ID" "$update_tag_data" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "PUT /api/v1/tags/$TAG_ID" "$status_code" "200" "$response"
fi

# Test Content Management
echo -e "${YELLOW}📄 Testing Content Management...${NC}"
echo ""

# Create content
if [ ! -z "$TAG_ID" ]; then
    content_data='{"link":"https://example.com/test-article","type":"artical","title":"Test Article for API Testing","tags":["'$TAG_ID'"]}'
else
    content_data='{"link":"https://example.com/test-article","type":"artical","title":"Test Article for API Testing"}'
fi

result=$(make_request "POST" "/api/v1/user/content" "$content_data" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "POST /api/v1/user/content" "$status_code" "200" "$response"

# Extract content ID
if [ "$status_code" = "200" ]; then
    CONTENT_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
fi

# Get user content
result=$(make_request "GET" "/api/v1/user/content?page=1&limit=10" "" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "GET /api/v1/user/content" "$status_code" "200" "$response"

# Get content stats
result=$(make_request "GET" "/api/v1/user/content/stats" "" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "GET /api/v1/user/content/stats" "$status_code" "200" "$response"

# Get content by ID
if [ ! -z "$CONTENT_ID" ]; then
    result=$(make_request "GET" "/api/v1/user/content/$CONTENT_ID" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "GET /api/v1/user/content/$CONTENT_ID" "$status_code" "200" "$response"
    
    # Update content
    update_content_data='{"title":"Updated Test Article"}'
    result=$(make_request "PUT" "/api/v1/user/content/$CONTENT_ID" "$update_content_data" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "PUT /api/v1/user/content/$CONTENT_ID" "$status_code" "200" "$response"
fi

# Test Shareable Links
echo -e "${YELLOW}🔗 Testing Shareable Links...${NC}"
echo ""

if [ ! -z "$CONTENT_ID" ]; then
    # Create shareable link
    link_data='{"contentId":"'$CONTENT_ID'"}'
    result=$(make_request "POST" "/api/v1/links" "$link_data" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "POST /api/v1/links" "$status_code" "201" "$response"
    
    # Extract link ID and short code
    if [ "$status_code" = "201" ]; then
        LINK_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        SHORT_CODE=$(echo "$response" | grep -o '"shortCode":"[^"]*"' | cut -d'"' -f4)
        
        # Test public access to shared content
        if [ ! -z "$SHORT_CODE" ]; then
            result=$(make_request "GET" "/share/$SHORT_CODE" "" "")
            status_code=$(echo "$result" | cut -d'|' -f1)
            response=$(echo "$result" | cut -d'|' -f2)
            print_test "GET /share/$SHORT_CODE" "$status_code" "200" "$response"
        fi
    fi
fi

# Test AI Summaries
echo -e "${YELLOW}🤖 Testing AI Summaries...${NC}"
echo ""

if [ ! -z "$CONTENT_ID" ]; then
    # Generate quick summary
    summary_data='{"contentId":"'$CONTENT_ID'"}'
    result=$(make_request "POST" "/api/v1/summaries/quick" "$summary_data" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    # Accept both 200 and 202 for async processing
    if [ "$status_code" = "200" ] || [ "$status_code" = "202" ]; then
        print_test "POST /api/v1/summaries/quick" "$status_code" "200/202" "$response"
    else
        print_test "POST /api/v1/summaries/quick" "$status_code" "200/202" "$response"
    fi
    
    # Generate general summary
    general_summary_data='{"contentId":"'$CONTENT_ID'","type":"quick"}'
    result=$(make_request "POST" "/api/v1/summaries" "$general_summary_data" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    if [ "$status_code" = "200" ] || [ "$status_code" = "202" ]; then
        print_test "POST /api/v1/summaries" "$status_code" "200/202" "$response"
        SUMMARY_ID=$(echo "$response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    else
        print_test "POST /api/v1/summaries" "$status_code" "200/202" "$response"
    fi
    
    # Get user summaries
    result=$(make_request "GET" "/api/v1/summaries?page=1&limit=10" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "GET /api/v1/summaries" "$status_code" "200" "$response"
    
    # Get summary by ID
    if [ ! -z "$SUMMARY_ID" ]; then
        result=$(make_request "GET" "/api/v1/summaries/$SUMMARY_ID" "" "$ACCESS_TOKEN")
        status_code=$(echo "$result" | cut -d'|' -f1)
        response=$(echo "$result" | cut -d'|' -f2)
        print_test "GET /api/v1/summaries/$SUMMARY_ID" "$status_code" "200" "$response"
    fi
fi

# Test Cleanup (Delete Operations)
echo -e "${YELLOW}🧹 Testing Cleanup (Delete Operations)...${NC}"
echo ""

# Delete summary
if [ ! -z "$SUMMARY_ID" ]; then
    result=$(make_request "DELETE" "/api/v1/summaries/$SUMMARY_ID" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "DELETE /api/v1/summaries/$SUMMARY_ID" "$status_code" "200" "$response"
fi

# Delete shareable link
if [ ! -z "$LINK_ID" ]; then
    result=$(make_request "DELETE" "/api/v1/links/$LINK_ID" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "DELETE /api/v1/links/$LINK_ID" "$status_code" "200" "$response"
fi

# Delete content
if [ ! -z "$CONTENT_ID" ]; then
    result=$(make_request "DELETE" "/api/v1/user/content/$CONTENT_ID" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "DELETE /api/v1/user/content/$CONTENT_ID" "$status_code" "200" "$response"
fi

# Delete tag
if [ ! -z "$TAG_ID" ]; then
    result=$(make_request "DELETE" "/api/v1/tags/$TAG_ID" "" "$ACCESS_TOKEN")
    status_code=$(echo "$result" | cut -d'|' -f1)
    response=$(echo "$result" | cut -d'|' -f2)
    print_test "DELETE /api/v1/tags/$TAG_ID" "$status_code" "200" "$response"
fi

# Test bulk delete
bulk_delete_data='{"contentIds":[]}'
result=$(make_request "DELETE" "/api/v1/user/content/bulk" "$bulk_delete_data" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "DELETE /api/v1/user/content/bulk" "$status_code" "200" "$response"

# Logout
result=$(make_request "POST" "/api/v1/logout" "{}" "$ACCESS_TOKEN")
status_code=$(echo "$result" | cut -d'|' -f1)
response=$(echo "$result" | cut -d'|' -f2)
print_test "POST /api/v1/logout" "$status_code" "200" "$response"

echo "============================================================"
echo -e "${GREEN}✅ All tests completed!${NC}"
echo ""
echo "Note: Some AI summary tests might show 202 status (pending) which is normal."
echo "Rate limiting may affect some tests if run multiple times quickly."
